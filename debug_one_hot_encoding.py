#!/usr/bin/env python
# coding: utf-8

import pandas as pd
import numpy as np

def debug_column_processing(raw_data_file_path, column_name):
    """
    Debug function to examine how a specific column is being processed
    """
    # Open file and read raw data
    infile = open(raw_data_file_path, encoding="utf-8", errors="ignore")
    raw_data = pd.read_csv(infile, header=11)
    infile.close()
    
    # Drop first blank column
    raw_data.drop(raw_data.columns[0], axis=1, inplace=True)
    
    # Get the specific column
    if column_name not in raw_data.columns:
        print(f"Column '{column_name}' not found in data!")
        print("Available columns:")
        for col in raw_data.columns:
            print(f"  - {col}")
        return
    
    column = raw_data[column_name]
    
    print(f"=== DEBUGGING COLUMN: {column_name} ===")
    print(f"Column data type: {column.dtype}")
    print(f"Total rows: {len(column)}")
    print(f"Non-null rows: {len(column.dropna())}")
    print(f"Null rows: {len(column) - len(column.dropna())}")
    
    # Get unique values
    uniques = sorted(column.dropna().unique())
    print(f"\nUnique values found: {len(uniques)}")
    for i, unique_val in enumerate(uniques):
        count = len(column[column == unique_val])
        print(f"  {i+1}. '{unique_val}' -> {count} occurrences")
    
    # Show some sample data
    print(f"\nFirst 10 non-null values:")
    non_null_sample = column.dropna().head(10)
    for idx, val in non_null_sample.items():
        print(f"  Row {idx}: '{val}'")
    
    # Simulate the decompose function logic
    print(f"\n=== SIMULATING ONE-HOT ENCODING ===")
    scaleRangeMin = 0
    scaleRangeMax = 1
    
    for i, unique_val in enumerate(uniques):
        col_name = f"{column_name}__{unique_val}"
        print(f"\nProcessing: {col_name}")
        
        # Count matches
        matches = len(column[(column == unique_val) & ~(column.isnull())])
        non_matches = len(column[(column != unique_val) & ~(column.isnull())])
        nulls = len(column[column.isnull()])
        
        print(f"  Matches (will get {scaleRangeMax}): {matches}")
        print(f"  Non-matches (will get {scaleRangeMin}): {non_matches}")
        print(f"  Nulls (will stay null): {nulls}")
        
        # Show some examples
        match_examples = column[(column == unique_val) & ~(column.isnull())].head(3)
        if len(match_examples) > 0:
            print(f"  Example matches: {list(match_examples.values)}")

def compare_columns(raw_data_file_path, working_column, problem_column):
    """
    Compare a working column with a problematic column
    """
    # Open file and read raw data
    infile = open(raw_data_file_path, encoding="utf-8", errors="ignore")
    raw_data = pd.read_csv(infile, header=11)
    infile.close()
    
    # Drop first blank column
    raw_data.drop(raw_data.columns[0], axis=1, inplace=True)
    
    print("=== COLUMN COMPARISON ===")
    
    for col_name in [working_column, problem_column]:
        if col_name not in raw_data.columns:
            print(f"Column '{col_name}' not found!")
            continue
            
        column = raw_data[col_name]
        uniques = sorted(column.dropna().unique())
        
        print(f"\n{col_name}:")
        print(f"  Data type: {column.dtype}")
        print(f"  Unique values: {uniques}")
        print(f"  Sample values: {list(column.dropna().head(5).values)}")

# Example usage:
if __name__ == "__main__":
    # You'll need to update these paths and column names
    raw_data_path = "/mnt/c/Users/<USER>/Downloads/Segmentation_3010/altman_telco_use_of_ai_86n_v3.csv"

    # Debug the problematic column (using the correct column name from the output)
    problem_column = "What are the top three challenges your organisation faces in adopting and scaling GenAI initiatives?\n\nSelect up to three - Selected Choice - Lack of clear ROI or commercial alignment"

    debug_column_processing(raw_data_path, problem_column)

    # Let's also check a working column for comparison
    working_column = "What are your main motivations for driving GenAI ambition?\nSelect all that apply - Selected Choice - Revenue uplift in core businesses (e.g. connectivity, consumer services)"

    print("\n" + "="*80)
    print("COMPARING WITH A WORKING COLUMN:")
    print("="*80)
    debug_column_processing(raw_data_path, working_column)
