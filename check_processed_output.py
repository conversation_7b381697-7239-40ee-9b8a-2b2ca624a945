#!/usr/bin/env python
# coding: utf-8

import pandas as pd
import numpy as np

def check_processed_output(processed_file_path):
    """
    Check the actual processed output to see what values are in the one-hot encoded columns
    """
    try:
        # Read the processed file
        processed_data = pd.read_csv(processed_file_path)
        
        print(f"=== PROCESSED FILE ANALYSIS ===")
        print(f"Total rows: {len(processed_data)}")
        print(f"Total columns: {len(processed_data.columns)}")
        
        # Look for columns related to the GenAI challenges question
        genai_challenge_cols = [col for col in processed_data.columns if "challenges your organisation faces" in col and "Lack of clear ROI" in col]
        
        print(f"\n=== GenAI CHALLENGES COLUMNS ===")
        for col in genai_challenge_cols:
            print(f"\nColumn: {col}")
            values = processed_data[col]
            print(f"  Data type: {values.dtype}")
            print(f"  Unique values: {sorted(values.dropna().unique())}")
            print(f"  Value counts:")
            print(f"    {values.value_counts(dropna=False).to_dict()}")
            
            # Show some sample values
            non_null_samples = values.dropna().head(5)
            if len(non_null_samples) > 0:
                print(f"  Sample non-null values: {list(non_null_samples.values)}")
        
        # Compare with a working column (motivations)
        motivation_cols = [col for col in processed_data.columns if "motivations for driving GenAI" in col and "Revenue uplift in core" in col]
        
        print(f"\n=== MOTIVATION COLUMNS (FOR COMPARISON) ===")
        for col in motivation_cols:
            print(f"\nColumn: {col}")
            values = processed_data[col]
            print(f"  Data type: {values.dtype}")
            print(f"  Unique values: {sorted(values.dropna().unique())}")
            print(f"  Value counts:")
            print(f"    {values.value_counts(dropna=False).to_dict()}")
            
            # Show some sample values
            non_null_samples = values.dropna().head(5)
            if len(non_null_samples) > 0:
                print(f"  Sample non-null values: {list(non_null_samples.values)}")
        
        # Let's also check a few other challenge columns to see the pattern
        other_challenge_cols = [col for col in processed_data.columns if "challenges your organisation faces" in col][:5]
        
        print(f"\n=== OTHER CHALLENGE COLUMNS (SAMPLE) ===")
        for col in other_challenge_cols:
            print(f"\nColumn: {col}")
            values = processed_data[col]
            unique_vals = sorted(values.dropna().unique())
            print(f"  Unique values: {unique_vals}")
            if len(unique_vals) > 0:
                print(f"  Value counts: {values.value_counts(dropna=False).to_dict()}")
        
    except Exception as e:
        print(f"Error reading processed file: {e}")

if __name__ == "__main__":
    processed_file_path = "/mnt/c/Users/<USER>/Downloads/Segmentation_3010/altman_telco_use_of_ai_86n_v3processed.csv"
    check_processed_output(processed_file_path)
