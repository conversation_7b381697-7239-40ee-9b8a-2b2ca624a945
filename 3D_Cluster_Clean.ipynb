#%% md
# 3D Cluster Visualization - Clean Version
Creates a clean 3D visualization of clusters with:
- Blue, red, orange dots with black borders
- Color-matched dotted lines dropping from each point
- Clean white background with no grid or labels
- Minimal cube outline
#%%
import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D

# Try to import plotly
try:
    import plotly.graph_objects as go
    PLOTLY_AVAILABLE = True
    print("✅ Plotly is available - Interactive plots enabled")
except ImportError:
    PLOTLY_AVAILABLE = False
    print("⚠️  Plotly not available - Using matplotlib only")

plt.style.use("ggplot")
plt.rcParams['figure.facecolor'] = 'white'
#%%
# Configuration
data_location = "/mnt/c/Users/<USER>/Downloads/segmentation_3010/30102025050705.xlsx"
user_col_name = "sys_Respondent"
entity = "user"
output_dir = "/mnt/c/Users/<USER>/Downloads/segmentation_3010/"
#%%
# Load data
print("Loading data...")
latent_factors_df = pd.read_excel(data_location, sheet_name="Users")
clusters_df = pd.read_excel(data_location, sheet_name="Clusters (Users)")
assignments_df = pd.read_excel(data_location, sheet_name="Cluster Assignment (Users)")

print(f"Loaded {len(latent_factors_df)} users with {latent_factors_df.shape[1]-2} latent factors")
print(f"Found {len(clusters_df)} clusters")
#%%
# Prepare data for plotting
entity_info_cols = [user_col_name, "user_idx"]
latent_factors_only = latent_factors_df.drop(entity_info_cols, axis=1)
factor_cols = latent_factors_only.columns[:3]  # First 3 latent factors

plot_df = pd.DataFrame({
    'Factor_1': latent_factors_only[factor_cols[0]],
    'Factor_2': latent_factors_only[factor_cols[1]], 
    'Factor_3': latent_factors_only[factor_cols[2]],
    'Cluster': assignments_df['cluster'],
    'Entity_ID': assignments_df[user_col_name] if user_col_name in assignments_df.columns else range(len(assignments_df))
})

unique_clusters = sorted(plot_df['Cluster'].unique())
print(f"Clusters found: {unique_clusters}")
print(f"Using factors: {list(factor_cols)}")
#%%
# Create color maps - Blue, Red, Orange
custom_colors = ['#1f77b4', '#d62728', '#ff7f0e']  # Blue, Red, Orange
plotly_color_map = {cluster: custom_colors[i % len(custom_colors)] for i, cluster in enumerate(unique_clusters)}

# Matplotlib compatible colors
import matplotlib.colors as mcolors
mpl_color_map = {cluster: mcolors.to_rgba(custom_colors[i % len(custom_colors)]) 
                 for i, cluster in enumerate(unique_clusters)}

print(f"Color scheme: Blue, Red, Orange for {len(unique_clusters)} clusters")
#%% md
## Interactive 3D Plot with Plotly
#%%
if PLOTLY_AVAILABLE:
    print("Creating clean 3D visualization...")
    
    fig = go.Figure()
    min_z = plot_df['Factor_3'].min()
    
    # Add scatter points
    for cluster in unique_clusters:
        cluster_data = plot_df[plot_df['Cluster'] == cluster]
        cluster_size = len(cluster_data)
        
        fig.add_trace(go.Scatter3d(
            x=cluster_data['Factor_1'],
            y=cluster_data['Factor_2'],
            z=cluster_data['Factor_3'],
            mode='markers',
            marker=dict(
                size=8,
                color=plotly_color_map[cluster],
                opacity=0.8,
                line=dict(width=2, color='black')
            ),
            name=f'Cluster {cluster} ({cluster_size} users)',
            text=cluster_data['Entity_ID'],
            hovertemplate=
                f'<b>Cluster {cluster}</b><br>' +
                'Factor 1: %{x:.3f}<br>' +
                'Factor 2: %{y:.3f}<br>' +
                'Factor 3: %{z:.3f}<br>' +
                'User ID: %{text}<br>' +
                '<extra></extra>'
        ))
    
    # Add color-matched dotted lines
    for cluster in unique_clusters:
        cluster_data = plot_df[plot_df['Cluster'] == cluster]
        dot_color = plotly_color_map[cluster]
        
        for idx, row in cluster_data.iterrows():
            fig.add_trace(go.Scatter3d(
                x=[row['Factor_1'], row['Factor_1']],
                y=[row['Factor_2'], row['Factor_2']],
                z=[row['Factor_3'], min_z],
                mode='lines',
                line=dict(
                    color=dot_color,
                    width=1.5,
                    dash='dot'
                ),
                showlegend=False,
                hoverinfo='skip'
            ))
    
    # Add minimal base outline
    x_range = [plot_df['Factor_1'].min(), plot_df['Factor_1'].max()]
    y_range = [plot_df['Factor_2'].min(), plot_df['Factor_2'].max()]
    
    base_outline = [
        ([x_range[0], x_range[1]], [y_range[0], y_range[0]], [min_z, min_z]),
        ([x_range[1], x_range[1]], [y_range[0], y_range[1]], [min_z, min_z]),
        ([x_range[1], x_range[0]], [y_range[1], y_range[1]], [min_z, min_z]),
        ([x_range[0], x_range[0]], [y_range[1], y_range[0]], [min_z, min_z])
    ]
    
    for x_coords, y_coords, z_coords in base_outline:
        fig.add_trace(go.Scatter3d(
            x=x_coords, y=y_coords, z=z_coords,
            mode='lines',
            line=dict(color='rgba(0, 0, 0, 0.3)', width=1),
            showlegend=False,
            hoverinfo='skip'
        ))
    
    # Clean layout
    fig.update_layout(
        title={
            'text': '3D Cluster Visualization',
            'x': 0.5,
            'xanchor': 'center',
            'font': {'size': 20}
        },
        scene=dict(
            xaxis=dict(title='', showgrid=False, showticklabels=False, 
                      showline=False, zeroline=False, showspikes=False),
            yaxis=dict(title='', showgrid=False, showticklabels=False,
                      showline=False, zeroline=False, showspikes=False),
            zaxis=dict(title='', showgrid=False, showticklabels=False,
                      showline=False, zeroline=False, showspikes=False),
            camera=dict(eye=dict(x=1.5, y=1.5, z=1.5)),
            bgcolor='white'
        ),
        width=1000,
        height=800,
        legend=dict(yanchor="top", y=0.99, xanchor="left", x=0.01)
    )
    
    fig.show()
    
    # Save
    output_file = os.path.join(output_dir, "3d_cluster_clean.html")
    fig.write_html(output_file)
    print(f"Saved to: {output_file}")
    
else:
    print("Plotly not available. Install with: pip install plotly")
#%% md
## Static 3D Plot with Matplotlib
#%%
# Create matplotlib version
fig = plt.figure(figsize=(12, 10))
ax = fig.add_subplot(111, projection='3d')

for cluster in unique_clusters:
    cluster_data = plot_df[plot_df['Cluster'] == cluster]
    cluster_size = len(cluster_data)
    
    ax.scatter(
        cluster_data['Factor_1'],
        cluster_data['Factor_2'], 
        cluster_data['Factor_3'],
        c=mpl_color_map[cluster],
        label=f'Cluster {cluster} ({cluster_size} users)',
        alpha=0.8,
        s=60,
        edgecolors='black',
        linewidth=1
    )

ax.set_title('3D Cluster Visualization', fontsize=16, fontweight='bold', pad=20)
ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
ax.view_init(elev=20, azim=45)
ax.grid(True, alpha=0.3)

plt.tight_layout()

# Save
output_file = os.path.join(output_dir, "3d_cluster_clean_static.png")
plt.savefig(output_file, dpi=300, bbox_inches='tight', facecolor='white')
print(f"Static plot saved to: {output_file}")

plt.show()
#%% md
## Summary

This clean notebook creates:
1. **Interactive 3D plot** with blue, red, orange dots
2. **Color-matched dotted lines** dropping from each point
3. **Clean white background** with no grid or axis labels
4. **Minimal base outline** for reference
5. **Static matplotlib version** for presentations

All files saved to your Downloads folder!