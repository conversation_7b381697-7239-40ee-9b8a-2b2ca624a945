#!/usr/bin/env python
# coding: utf-8

import pandas as pd
import numpy as np

def check_column_parameters(raw_data_file_path):
    """
    Check the column parameters (first 12 rows) to see how different columns are configured
    """
    try:
        # Read the column parameters
        infile = open(raw_data_file_path, encoding="utf-8", errors="ignore")
        column_params = pd.read_csv(infile, header=None, nrows=12, index_col=0).T
        infile.close()

        # Drop blank column
        column_params.drop(column_params.columns[len(column_params.columns)-2], axis=1, inplace=True)

        # Fix column name capitalization
        column_params = column_params.rename(columns={'Include': 'include'})

        # Set column data types
        column_params = column_params.astype(dtype = {'include': 'float',
                  'varType': 'str',
                  'nullMode': 'str',
                  'decompose': 'float',
                  'scale': 'float',
                  'minMode': 'str',
                  'min': 'float',
                  'maxMode': 'str',
                   'max': 'float',
                   'invert':'float',
                   'colName': 'str'})

        # Now use colName to find the actual column names
        column_params['actual_col_name'] = column_params['colName']
        
        print("=== COLUMN PARAMETERS ANALYSIS ===")
        print(f"Total columns with parameters: {len(column_params)}")

        # First, let's see what the actual column names look like
        print(f"\n=== FIRST 10 ACTUAL COLUMN NAMES ===")
        for i, col_name in enumerate(list(column_params['colName'])[:10]):
            print(f"{i+1}. {repr(col_name)}")

        # Look for the problematic GenAI challenges columns
        challenge_mask = column_params['colName'].str.contains("challenges", case=False, na=False) & column_params['colName'].str.contains("Lack of clear ROI", case=False, na=False)
        genai_challenge_rows = column_params[challenge_mask]

        print(f"\n=== GenAI CHALLENGES COLUMN PARAMETERS ===")
        print(f"Found {len(genai_challenge_rows)} matching columns")
        for idx, row in genai_challenge_rows.iterrows():
            print(f"\nColumn: {row['colName']}")
            for param_name in column_params.columns:
                print(f"  {param_name}: {row[param_name]}")

        # Compare with working motivation columns
        motivation_mask = column_params['colName'].str.contains("motivations", case=False, na=False) & column_params['colName'].str.contains("Revenue uplift", case=False, na=False)
        motivation_rows = column_params[motivation_mask]

        print(f"\n=== MOTIVATION COLUMN PARAMETERS (FOR COMPARISON) ===")
        print(f"Found {len(motivation_rows)} matching columns")
        for idx, row in motivation_rows.iterrows():
            print(f"\nColumn: {row['colName']}")
            for param_name in column_params.columns:
                print(f"  {param_name}: {row[param_name]}")

        # Let's check a few other challenge columns
        other_challenge_mask = column_params['colName'].str.contains("challenges", case=False, na=False)
        other_challenge_rows = column_params[other_challenge_mask].head(3)

        print(f"\n=== OTHER CHALLENGE COLUMN PARAMETERS (SAMPLE) ===")
        total_challenge_cols = len(column_params[other_challenge_mask])
        print(f"Found {total_challenge_cols} total challenge columns")
        for idx, row in other_challenge_rows.iterrows():
            print(f"\nColumn: {row['colName']}")
            key_params = ['include', 'decompose', 'invert']
            for param_name in key_params:
                if param_name in column_params.columns:
                    print(f"  {param_name}: {row[param_name]}")

        # Let's also just show all column names to see what we're working with
        print(f"\n=== SAMPLE COLUMN NAMES ===")
        sample_mask = column_params['colName'].str.contains("challenges|motivations", case=False, na=False)
        sample_rows = column_params[sample_mask].head(10)
        for i, (idx, row) in enumerate(sample_rows.iterrows()):
            print(f"{i+1}. {row['colName']}")
        
        # Check if there are any differences in the 'invert' parameter
        print(f"\n=== INVERT PARAMETER ANALYSIS ===")
        
        # Get all included columns that are decomposed
        included_decomposed = column_params[(column_params['include'] == 1) & (column_params['decompose'] == 1)]
        
        invert_1_cols = included_decomposed[included_decomposed['invert'] == 1]
        invert_0_cols = included_decomposed[included_decomposed['invert'] == 0]
        
        print(f"Columns with invert=1: {len(invert_1_cols)}")
        if len(invert_1_cols) > 0:
            print("Sample columns with invert=1:")
            for col in list(invert_1_cols.index)[:3]:
                print(f"  - {col}")
        
        print(f"\nColumns with invert=0: {len(invert_0_cols)}")
        if len(invert_0_cols) > 0:
            print("Sample columns with invert=0:")
            for col in list(invert_0_cols.index)[:3]:
                print(f"  - {col}")
        
    except Exception as e:
        print(f"Error reading column parameters: {e}")

if __name__ == "__main__":
    raw_data_file_path = "/mnt/c/Users/<USER>/Downloads/Segmentation_3010/altman_telco_use_of_ai_86n_v3.csv"
    check_column_parameters(raw_data_file_path)
