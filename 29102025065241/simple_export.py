#!/usr/bin/env python3
"""
Simple script to export output.xlsx directly to Downloads folder
No modifications, just a direct copy
"""

import shutil
import os

def main():
    """Copy output.xlsx directly to Downloads folder"""
    
    # Try multiple possible source paths
    possible_sources = [
        "29102025065241/output.xlsx",  # From current directory
        "../output.xlsx",  # From model directory
        "output.xlsx",  # If running from 29102025065241 directory
    ]
    
    source_file = None
    for path in possible_sources:
        if os.path.exists(path):
            source_file = path
            break
    
    # Destination file in Downloads
    destination_file = "/mnt/c/Users/<USER>/Downloads/segmentation_2910/29102025065241.xlsx"
    
    print(f"🔍 Current working directory: {os.getcwd()}")
    print(f"🎯 Destination: {destination_file}")
    
    if source_file is None:
        print("❌ Source file not found in any of these locations:")
        for path in possible_sources:
            print(f"   - {path} (exists: {os.path.exists(path)})")
        return 1
    
    print(f"✅ Found source file: {source_file}")
    
    try:
        # Create destination directory if it doesn't exist
        os.makedirs(os.path.dirname(destination_file), exist_ok=True)
        
        # Copy the file directly
        shutil.copy2(source_file, destination_file)
        
        # Check file size
        file_size = os.path.getsize(destination_file) / (1024 * 1024)  # MB
        
        print(f"✅ File copied successfully!")
        print(f"📁 File size: {file_size:.2f} MB")
        print(f"📋 You can find your file at: {destination_file}")
        
        return 0
        
    except Exception as e:
        print(f"❌ Error copying file: {e}")
        return 1

if __name__ == "__main__":
    exit(main())
