#%% md
# 3D Cluster Visualization (Matplotlib Only)
This notebook creates 3D plots of the clusters using only matplotlib - no additional dependencies required.
#%%
import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import matplotlib.colors as mcolors

# Set style
plt.style.use("ggplot")
plt.rcParams['figure.facecolor'] = 'white'
#%%
# Configuration
data_location = "/mnt/c/Users/<USER>/Downloads/segmentation_3010/30102025050705.xlsx"
user_col_name = "sys_Respondent"
entity = "user"  # or "item"
output_dir = "/mnt/c/Users/<USER>/Downloads/segmentation_3010/"
#%% md
## Load Data
#%%
# Determine sheet names based on entity type
if entity == "user":
    sheet_name = "Users"
    entity_info_cols = [user_col_name, "user_idx"]
    cluster_info_sheet_name = "Clusters (Users)"
    cluster_assignment_sheet_name = "Cluster Assignment (Users)"
    num_entities_col = "number_of_users"
else:
    sheet_name = "Items"
    entity_info_cols = ["item", "item_idx"]
    cluster_info_sheet_name = "Clusters (Items)"
    cluster_assignment_sheet_name = "Cluster Assignment (Items)"
    num_entities_col = "number_of_items"

# Load the data
print("Loading data...")
latent_factors_df = pd.read_excel(data_location, sheet_name=sheet_name)
clusters_df = pd.read_excel(data_location, sheet_name=cluster_info_sheet_name)
assignments_df = pd.read_excel(data_location, sheet_name=cluster_assignment_sheet_name)

print(f"Loaded {len(latent_factors_df)} {entity}s with {latent_factors_df.shape[1]-2} latent factors")
print(f"Found {len(clusters_df)} clusters")
print("Data loaded successfully!")
#%%
# Extract the three latent factors
latent_factors_only = latent_factors_df.drop(entity_info_cols, axis=1)
factor_cols = latent_factors_only.columns[:3]  # Take first 3 latent factors

print(f"Using latent factors: {list(factor_cols)}")
print(f"Latent factors shape: {latent_factors_only[factor_cols].shape}")

# Create the plotting dataframe
plot_df = pd.DataFrame({
    'Factor_1': latent_factors_only[factor_cols[0]],
    'Factor_2': latent_factors_only[factor_cols[1]], 
    'Factor_3': latent_factors_only[factor_cols[2]],
    'Cluster': assignments_df['cluster']
})

print(f"Plot dataframe shape: {plot_df.shape}")
print(f"Clusters found: {sorted(plot_df['Cluster'].unique())}")
#%% md
## Create 3D Visualization
#%%
# Create color palette
unique_clusters = sorted(plot_df['Cluster'].unique())
n_clusters = len(unique_clusters)

# Use matplotlib's tab10 colormap for up to 10 clusters, then cycle through
colors = plt.cm.tab10(np.linspace(0, 1, min(10, n_clusters)))
if n_clusters > 10:
    colors = plt.cm.tab20(np.linspace(0, 1, n_clusters))

color_map = {cluster: colors[i % len(colors)] for i, cluster in enumerate(unique_clusters)}

print(f"Created color map for {n_clusters} clusters")
#%%
# Create the main 3D plot
fig = plt.figure(figsize=(16, 12))
ax = fig.add_subplot(111, projection='3d')

# Plot each cluster
for cluster in unique_clusters:
    cluster_data = plot_df[plot_df['Cluster'] == cluster]
    
    # Get cluster size from clusters_df
    try:
        cluster_size = clusters_df[clusters_df['cluster'] == cluster][num_entities_col].values[0]
    except:
        cluster_size = len(cluster_data)
    
    ax.scatter(
        cluster_data['Factor_1'],
        cluster_data['Factor_2'], 
        cluster_data['Factor_3'],
        c=[color_map[cluster]],
        label=f'Cluster {cluster} ({cluster_size} {entity}s)',
        alpha=0.7,
        s=60,
        edgecolors='black',
        linewidth=0.5
    )

# Customize the plot
ax.set_xlabel(f'Latent Factor 1\n({factor_cols[0]})', fontsize=12, labelpad=10)
ax.set_ylabel(f'Latent Factor 2\n({factor_cols[1]})', fontsize=12, labelpad=10)
ax.set_zlabel(f'Latent Factor 3\n({factor_cols[2]})', fontsize=12, labelpad=10)

ax.set_title(f'3D Cluster Visualization\n{entity.title()}s in Latent Factor Space', 
             fontsize=18, fontweight='bold', pad=30)

# Add legend
ax.legend(bbox_to_anchor=(1.15, 1), loc='upper left', fontsize=11)

# Set viewing angle for better perspective
ax.view_init(elev=20, azim=45)

# Add grid
ax.grid(True, alpha=0.3)

# Adjust layout
plt.tight_layout()

# Save the plot
output_file = os.path.join(output_dir, f"3d_cluster_visualization_{entity}s_matplotlib.png")
plt.savefig(output_file, dpi=300, bbox_inches='tight', facecolor='white')
print(f"3D plot saved to: {output_file}")

plt.show()
#%% md
## Multiple Viewing Angles
#%%
# Create multiple views of the same data
fig = plt.figure(figsize=(20, 15))

# Different viewing angles
angles = [(20, 45), (20, 135), (60, 45), (20, -45)]
titles = ['Front View', 'Side View', 'Top View', 'Back View']

for i, (elev, azim) in enumerate(angles):
    ax = fig.add_subplot(2, 2, i+1, projection='3d')
    
    # Plot each cluster
    for cluster in unique_clusters:
        cluster_data = plot_df[plot_df['Cluster'] == cluster]
        
        ax.scatter(
            cluster_data['Factor_1'],
            cluster_data['Factor_2'], 
            cluster_data['Factor_3'],
            c=[color_map[cluster]],
            label=f'Cluster {cluster}' if i == 0 else "",  # Only show legend on first plot
            alpha=0.7,
            s=40
        )
    
    # Customize each subplot
    ax.set_xlabel(f'Factor 1', fontsize=10)
    ax.set_ylabel(f'Factor 2', fontsize=10)
    ax.set_zlabel(f'Factor 3', fontsize=10)
    ax.set_title(titles[i], fontsize=12, fontweight='bold')
    ax.view_init(elev=elev, azim=azim)
    ax.grid(True, alpha=0.3)
    
    # Add legend only to first subplot
    if i == 0:
        ax.legend(bbox_to_anchor=(1.3, 1), loc='upper left', fontsize=9)

plt.suptitle(f'3D Cluster Visualization - Multiple Views\n{entity.title()}s in Latent Factor Space', 
             fontsize=16, fontweight='bold')
plt.tight_layout()

# Save the multi-view plot
output_file = os.path.join(output_dir, f"3d_cluster_multiview_{entity}s.png")
plt.savefig(output_file, dpi=300, bbox_inches='tight', facecolor='white')
print(f"Multi-view 3D plot saved to: {output_file}")

plt.show()
#%% md
## 2D Projections
#%%
# Create 2D projections
fig, axes = plt.subplots(1, 3, figsize=(18, 6))

# Factor 1 vs Factor 2
for cluster in unique_clusters:
    cluster_data = plot_df[plot_df['Cluster'] == cluster]
    axes[0].scatter(cluster_data['Factor_1'], cluster_data['Factor_2'], 
                   c=[color_map[cluster]], label=f'Cluster {cluster}', alpha=0.7, s=50)
axes[0].set_xlabel(f'Latent Factor 1 ({factor_cols[0]})', fontsize=12)
axes[0].set_ylabel(f'Latent Factor 2 ({factor_cols[1]})', fontsize=12)
axes[0].set_title('Factor 1 vs Factor 2', fontsize=14, fontweight='bold')
axes[0].grid(True, alpha=0.3)

# Factor 1 vs Factor 3
for cluster in unique_clusters:
    cluster_data = plot_df[plot_df['Cluster'] == cluster]
    axes[1].scatter(cluster_data['Factor_1'], cluster_data['Factor_3'], 
                   c=[color_map[cluster]], label=f'Cluster {cluster}', alpha=0.7, s=50)
axes[1].set_xlabel(f'Latent Factor 1 ({factor_cols[0]})', fontsize=12)
axes[1].set_ylabel(f'Latent Factor 3 ({factor_cols[2]})', fontsize=12)
axes[1].set_title('Factor 1 vs Factor 3', fontsize=14, fontweight='bold')
axes[1].grid(True, alpha=0.3)

# Factor 2 vs Factor 3
for cluster in unique_clusters:
    cluster_data = plot_df[plot_df['Cluster'] == cluster]
    axes[2].scatter(cluster_data['Factor_2'], cluster_data['Factor_3'], 
                   c=[color_map[cluster]], label=f'Cluster {cluster}', alpha=0.7, s=50)
axes[2].set_xlabel(f'Latent Factor 2 ({factor_cols[1]})', fontsize=12)
axes[2].set_ylabel(f'Latent Factor 3 ({factor_cols[2]})', fontsize=12)
axes[2].set_title('Factor 2 vs Factor 3', fontsize=14, fontweight='bold')
axes[2].grid(True, alpha=0.3)

# Add legend to the last subplot
axes[2].legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=10)

plt.suptitle(f'2D Projections of {entity.title()} Clusters', fontsize=16, fontweight='bold')
plt.tight_layout()

# Save the plot
output_file = os.path.join(output_dir, f"2d_projections_{entity}s_simple.png")
plt.savefig(output_file, dpi=300, bbox_inches='tight', facecolor='white')
print(f"2D projections saved to: {output_file}")

plt.show()
#%% md
## Cluster Statistics
#%%
# Calculate and display cluster statistics
print("=" * 50)
print("CLUSTER ANALYSIS SUMMARY")
print("=" * 50)
print(f"Total {entity}s: {len(plot_df)}")
print(f"Number of clusters: {n_clusters}")
print(f"Latent factors used: {list(factor_cols)}")
print()

# Create a summary table
summary_data = []
for cluster in unique_clusters:
    cluster_data = plot_df[plot_df['Cluster'] == cluster]
    size = len(cluster_data)
    percentage = size / len(plot_df) * 100
    
    summary_data.append({
        'Cluster': cluster,
        'Size': size,
        'Percentage': f"{percentage:.1f}%",
        'Factor_1_Mean': f"{cluster_data['Factor_1'].mean():.3f}",
        'Factor_2_Mean': f"{cluster_data['Factor_2'].mean():.3f}",
        'Factor_3_Mean': f"{cluster_data['Factor_3'].mean():.3f}",
        'Factor_1_Std': f"{cluster_data['Factor_1'].std():.3f}",
        'Factor_2_Std': f"{cluster_data['Factor_2'].std():.3f}",
        'Factor_3_Std': f"{cluster_data['Factor_3'].std():.3f}"
    })

summary_df = pd.DataFrame(summary_data)
print("CLUSTER SUMMARY:")
print(summary_df.to_string(index=False))
print()

# Overall factor statistics
print("OVERALL FACTOR STATISTICS:")
print(f"Factor 1 - Range: [{plot_df['Factor_1'].min():.3f}, {plot_df['Factor_1'].max():.3f}], Mean: {plot_df['Factor_1'].mean():.3f}")
print(f"Factor 2 - Range: [{plot_df['Factor_2'].min():.3f}, {plot_df['Factor_2'].max():.3f}], Mean: {plot_df['Factor_2'].mean():.3f}")
print(f"Factor 3 - Range: [{plot_df['Factor_3'].min():.3f}, {plot_df['Factor_3'].max():.3f}], Mean: {plot_df['Factor_3'].mean():.3f}")

# Save summary to CSV
summary_file = os.path.join(output_dir, f"cluster_summary_{entity}s.csv")
summary_df.to_csv(summary_file, index=False)
print(f"\nCluster summary saved to: {summary_file}")
#%% md
## Summary

This simplified notebook creates:
1. **Main 3D Plot**: High-quality 3D visualization using matplotlib
2. **Multi-View 3D Plot**: Four different viewing angles of the same data
3. **2D Projections**: Three 2D views showing different factor combinations
4. **Detailed Statistics**: Comprehensive cluster analysis and summary table
5. **CSV Export**: Cluster summary data for further analysis

All visualizations are saved as high-resolution PNG files in your Downloads folder.