#!/usr/bin/env python3
"""
Test script to check Python environment and available packages
"""

import sys
import subprocess

print("=== PYTHON ENVIRONMENT INFO ===")
print(f"Python executable: {sys.executable}")
print(f"Python version: {sys.version}")
print(f"Python path: {sys.path[:3]}...")  # Show first 3 paths
print()

print("=== TESTING PACKAGE AVAILABILITY ===")

# Test basic packages
packages_to_test = [
    'numpy', 'pandas', 'matplotlib', 'plotly', 'seaborn'
]

for package in packages_to_test:
    try:
        __import__(package)
        print(f"✅ {package} - Available")
    except ImportError as e:
        print(f"❌ {package} - Not available: {e}")

print()
print("=== INSTALLATION COMMANDS ===")
print("If plotly is missing, try one of these commands:")
print("1. pip install plotly")
print("2. pip3 install plotly")
print("3. python -m pip install plotly")
print("4. python3 -m pip install plotly")

# Try to install plotly if missing
try:
    import plotly
    print("\n✅ Plotly is already available!")
except ImportError:
    print("\n❌ Plotly not found. Attempting to install...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "plotly"])
        print("✅ Plotly installation completed!")
    except Exception as e:
        print(f"❌ Failed to install plotly: {e}")
