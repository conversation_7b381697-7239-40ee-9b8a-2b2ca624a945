#%% md
# 3D Cluster Visualization (Robust Version)
This notebook creates 3D plots of the clusters using the three latent factors. It works with or without plotly.
#%%
import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D

# Try to import plotly, fall back to matplotlib-only if not available
try:
    import plotly.express as px
    import plotly.graph_objects as go
    PLOTLY_AVAILABLE = True
    print("✅ Plotly is available - Interactive plots enabled")
except ImportError:
    PLOTLY_AVAILABLE = False
    print("⚠️  Plotly not available - Using matplotlib only")
    print("   To install plotly: pip install plotly")

# Set style
plt.style.use("ggplot")
plt.rcParams['figure.facecolor'] = 'white'
#%%
# Configuration
data_location = "/mnt/c/Users/<USER>/Downloads/segmentation_3010/30102025050705.xlsx"
user_col_name = "sys_Respondent"
entity = "user"  # or "item"
output_dir = "/mnt/c/Users/<USER>/Downloads/segmentation_3010/"
#%% md
## Load Data
#%%
# Determine sheet names based on entity type
if entity == "user":
    sheet_name = "Users"
    entity_info_cols = [user_col_name, "user_idx"]
    cluster_info_sheet_name = "Clusters (Users)"
    cluster_assignment_sheet_name = "Cluster Assignment (Users)"
    num_entities_col = "number_of_users"
else:
    sheet_name = "Items"
    entity_info_cols = ["item", "item_idx"]
    cluster_info_sheet_name = "Clusters (Items)"
    cluster_assignment_sheet_name = "Cluster Assignment (Items)"
    num_entities_col = "number_of_items"

# Load the data
print("Loading data...")
latent_factors_df = pd.read_excel(data_location, sheet_name=sheet_name)
clusters_df = pd.read_excel(data_location, sheet_name=cluster_info_sheet_name)
assignments_df = pd.read_excel(data_location, sheet_name=cluster_assignment_sheet_name)

print(f"Loaded {len(latent_factors_df)} {entity}s with {latent_factors_df.shape[1]-2} latent factors")
print(f"Found {len(clusters_df)} clusters")
print("Data loaded successfully!")
#%%
# Extract the three latent factors
latent_factors_only = latent_factors_df.drop(entity_info_cols, axis=1)
factor_cols = latent_factors_only.columns[:3]  # Take first 3 latent factors

print(f"Using latent factors: {list(factor_cols)}")
print(f"Latent factors shape: {latent_factors_only[factor_cols].shape}")

# Create the plotting dataframe
plot_df = pd.DataFrame({
    'Factor_1': latent_factors_only[factor_cols[0]],
    'Factor_2': latent_factors_only[factor_cols[1]], 
    'Factor_3': latent_factors_only[factor_cols[2]],
    'Cluster': assignments_df['cluster'],
    'Entity_ID': assignments_df[entity_info_cols[0]] if entity_info_cols[0] in assignments_df.columns else range(len(assignments_df))
})

print(f"Plot dataframe shape: {plot_df.shape}")
print(f"Clusters found: {sorted(plot_df['Cluster'].unique())}")
#%% md
## Create Color Palette
#%%
# Create a color map for clusters
unique_clusters = sorted(plot_df['Cluster'].unique())
n_clusters = len(unique_clusters)

# Create consistent colors for both matplotlib and plotly: blue, red, orange
import matplotlib.colors as mcolors
custom_mpl_colors = ['#1f77b4', '#d62728', '#ff7f0e']  # Blue, Red, Orange

# Create matplotlib color map
mpl_color_map = {cluster: mcolors.to_rgba(custom_mpl_colors[i % len(custom_mpl_colors)]) 
                 for i, cluster in enumerate(unique_clusters)}

if PLOTLY_AVAILABLE:
    # Create custom plotly colors: blue, red, orange
    custom_colors = ['#1f77b4', '#d62728', '#ff7f0e']  # Blue, Red, Orange
    plotly_color_map = {cluster: custom_colors[i % len(custom_colors)] for i, cluster in enumerate(unique_clusters)}
else:
    plotly_color_map = None

print(f"Created color map for {n_clusters} clusters")
for cluster in unique_clusters:
    cluster_size = len(plot_df[plot_df['Cluster'] == cluster])
    print(f"Cluster {cluster}: {cluster_size} {entity}s")
#%% md
## Interactive 3D Plot with Plotly (if available)
#%%
if PLOTLY_AVAILABLE:
    print("Creating interactive 3D plot with Plotly...")
    
    # Create interactive 3D scatter plot
    fig = go.Figure()
    
    # First, find the minimum z-value to create the base plane
    min_z = plot_df['Factor_3'].min()
    
    for cluster in unique_clusters:
        cluster_data = plot_df[plot_df['Cluster'] == cluster]
        cluster_size = len(clusters_df[clusters_df['cluster'] == cluster][num_entities_col].values)
        if cluster_size > 0:
            cluster_size = clusters_df[clusters_df['cluster'] == cluster][num_entities_col].values[0]
        else:
            cluster_size = len(cluster_data)
        
        fig.add_trace(go.Scatter3d(
            x=cluster_data['Factor_1'],
            y=cluster_data['Factor_2'],
            z=cluster_data['Factor_3'],
            mode='markers',
            marker=dict(
                size=8,  # Slightly larger dots
                color=plotly_color_map[cluster] if plotly_color_map else mpl_color_map[cluster],
                opacity=0.8,  # Less transparent
                line=dict(width=2, color='black')  # Strong black border
            ),
            name=f'Cluster {cluster} ({cluster_size} {entity}s)',
            text=cluster_data['Entity_ID'],
            hovertemplate=
                f'<b>Cluster {cluster}</b><br>' +
                'Factor 1: %{x:.3f}<br>' +
                'Factor 2: %{y:.3f}<br>' +
                'Factor 3: %{z:.3f}<br>' +
                f'{entity.title()} ID: %{{text}}<br>' +
                '<extra></extra>'
        ))
    
    # Add dotted lines from each point down to the base (same color as dots)
    for cluster in unique_clusters:
        cluster_data = plot_df[plot_df['Cluster'] == cluster]
        dot_color = plotly_color_map[cluster] if plotly_color_map else mpl_color_map[cluster]
        for idx, row in cluster_data.iterrows():
            fig.add_trace(go.Scatter3d(
                x=[row['Factor_1'], row['Factor_1']],  # Same x position
                y=[row['Factor_2'], row['Factor_2']],  # Same y position
                z=[row['Factor_3'], min_z],  # From point down to base
                mode='lines',
                line=dict(
                    color=dot_color,  # Same color as the dots
                    width=1.5,  # Slightly thicker lines
                    dash='dot'  # Dotted style
                ),
                showlegend=False,  # Don't show in legend
                hoverinfo='skip'  # Don't show hover info for lines
            ))
    
    # Add shadow planes to match the reference image
    # Get the data ranges for creating shadow planes
    x_range = [plot_df['Factor_1'].min(), plot_df['Factor_1'].max()]
    y_range = [plot_df['Factor_2'].min(), plot_df['Factor_2'].max()]
    z_range = [plot_df['Factor_3'].min(), plot_df['Factor_3'].max()]
    
    # Create shadow plane on the bottom (XY plane at min Z)
    fig.add_trace(go.Mesh3d(
        x=[x_range[0], x_range[1], x_range[1], x_range[0]],
        y=[y_range[0], y_range[0], y_range[1], y_range[1]],
        z=[min_z, min_z, min_z, min_z],
        i=[0, 0],
        j=[1, 2],
        k=[2, 3],
        color='rgba(245, 245, 245, 0.8)',  # Bright light gray
        showlegend=False,
        hoverinfo='skip'
    ))
    
    # Create shadow plane on the back (XZ plane at max Y)
    fig.add_trace(go.Mesh3d(
        x=[x_range[0], x_range[1], x_range[1], x_range[0]],
        y=[y_range[1], y_range[1], y_range[1], y_range[1]],
        z=[z_range[0], z_range[0], z_range[1], z_range[1]],
        i=[0, 0],
        j=[1, 2],
        k=[2, 3],
        color='rgba(200, 200, 200, 0.08)',  # Even lighter shadow
        showlegend=False,
        hoverinfo='skip'
    ))
    
    # Create shadow plane on the left (YZ plane at min X)
    fig.add_trace(go.Mesh3d(
        x=[x_range[0], x_range[0], x_range[0], x_range[0]],
        y=[y_range[0], y_range[1], y_range[1], y_range[0]],
        z=[z_range[0], z_range[0], z_range[1], z_range[1]],
        i=[0, 0],
        j=[1, 2],
        k=[2, 3],
        color='rgba(200, 200, 200, 0.06)',  # Lightest shadow
        showlegend=False,
        hoverinfo='skip'
    ))
    
    # Update layout
    fig.update_layout(
        title={
            'text': f'Interactive 3D Cluster Visualization - {entity.title()}s in Latent Factor Space',
            'x': 0.5,
            'xanchor': 'center',
            'font': {'size': 20}
        },
        scene=dict(
            xaxis=dict(
                title='',  # Remove axis title
                showgrid=False,  # Remove grid
                showticklabels=False,  # Remove tick labels (0.2, 0.25, etc.)
                showline=False,  # Remove axis line
                zeroline=False,  # Remove zero line
                showspikes=False  # Remove spike lines
            ),
            yaxis=dict(
                title='',  # Remove axis title
                showgrid=False,  # Remove grid
                showticklabels=False,  # Remove tick labels
                showline=False,  # Remove axis line
                zeroline=False,  # Remove zero line
                showspikes=False  # Remove spike lines
            ),
            zaxis=dict(
                title='',  # Remove axis title
                showgrid=False,  # Remove grid
                showticklabels=False,  # Remove tick labels
                showline=False,  # Remove axis line
                zeroline=False,  # Remove zero line
                showspikes=False  # Remove spike lines
            ),
            camera=dict(
                eye=dict(x=1.5, y=1.5, z=1.5)
            ),
            bgcolor='rgba(248, 248, 248, 1)'  # Very light gray background to show shadows
        ),
        width=1000,
        height=800,
        legend=dict(
            yanchor="top",
            y=0.99,
            xanchor="left",
            x=0.01
        )
    )
    
    # Show the plot
    fig.show()
    
    # Save as HTML
    output_file = os.path.join(output_dir, f"3d_cluster_visualization_{entity}s_interactive.html")
    fig.write_html(output_file)
    print(f"Interactive 3D plot saved to: {output_file}")
    
else:
    print("Plotly not available - skipping interactive plot")
    print("Install plotly with: pip install plotly")
#%% md
## Static 3D Plot with Matplotlib
#%%
print("Creating static 3D plot with Matplotlib...")

# Create static 3D plot with matplotlib
fig = plt.figure(figsize=(16, 12))
ax = fig.add_subplot(111, projection='3d')

# Plot each cluster
for cluster in unique_clusters:
    cluster_data = plot_df[plot_df['Cluster'] == cluster]
    try:
        cluster_size = clusters_df[clusters_df['cluster'] == cluster][num_entities_col].values[0]
    except:
        cluster_size = len(cluster_data)
    
    ax.scatter(
        cluster_data['Factor_1'],
        cluster_data['Factor_2'], 
        cluster_data['Factor_3'],
        c=mpl_color_map[cluster],
        label=f'Cluster {cluster} ({cluster_size} {entity}s)',
        alpha=0.7,
        s=60,
        edgecolors='black',
        linewidth=0.5
    )

# Customize the plot
ax.set_xlabel(f'Latent Factor 1\n({factor_cols[0]})', fontsize=12, labelpad=10)
ax.set_ylabel(f'Latent Factor 2\n({factor_cols[1]})', fontsize=12, labelpad=10)
ax.set_zlabel(f'Latent Factor 3\n({factor_cols[2]})', fontsize=12, labelpad=10)

ax.set_title(f'3D Cluster Visualization\n{entity.title()}s in Latent Factor Space', 
             fontsize=18, fontweight='bold', pad=30)

# Add legend
ax.legend(bbox_to_anchor=(1.15, 1), loc='upper left', fontsize=11)

# Set viewing angle for better perspective
ax.view_init(elev=20, azim=45)

# Add grid
ax.grid(True, alpha=0.3)

# Adjust layout
plt.tight_layout()

# Save the plot
output_file = os.path.join(output_dir, f"3d_cluster_visualization_{entity}s_static.png")
plt.savefig(output_file, dpi=300, bbox_inches='tight', facecolor='white')
print(f"Static 3D plot saved to: {output_file}")

plt.show()
#%% md
## 2D Projections
#%%
# Create 2D projections
fig, axes = plt.subplots(1, 3, figsize=(18, 6))

# Factor 1 vs Factor 2
for cluster in unique_clusters:
    cluster_data = plot_df[plot_df['Cluster'] == cluster]
    axes[0].scatter(cluster_data['Factor_1'], cluster_data['Factor_2'], 
                   c=mpl_color_map[cluster], label=f'Cluster {cluster}', alpha=0.7, s=50)
axes[0].set_xlabel(f'Latent Factor 1 ({factor_cols[0]})', fontsize=12)
axes[0].set_ylabel(f'Latent Factor 2 ({factor_cols[1]})', fontsize=12)
axes[0].set_title('Factor 1 vs Factor 2', fontsize=14, fontweight='bold')
axes[0].grid(True, alpha=0.3)

# Factor 1 vs Factor 3
for cluster in unique_clusters:
    cluster_data = plot_df[plot_df['Cluster'] == cluster]
    axes[1].scatter(cluster_data['Factor_1'], cluster_data['Factor_3'], 
                   c=mpl_color_map[cluster], label=f'Cluster {cluster}', alpha=0.7, s=50)
axes[1].set_xlabel(f'Latent Factor 1 ({factor_cols[0]})', fontsize=12)
axes[1].set_ylabel(f'Latent Factor 3 ({factor_cols[2]})', fontsize=12)
axes[1].set_title('Factor 1 vs Factor 3', fontsize=14, fontweight='bold')
axes[1].grid(True, alpha=0.3)

# Factor 2 vs Factor 3
for cluster in unique_clusters:
    cluster_data = plot_df[plot_df['Cluster'] == cluster]
    axes[2].scatter(cluster_data['Factor_2'], cluster_data['Factor_3'], 
                   c=mpl_color_map[cluster], label=f'Cluster {cluster}', alpha=0.7, s=50)
axes[2].set_xlabel(f'Latent Factor 2 ({factor_cols[1]})', fontsize=12)
axes[2].set_ylabel(f'Latent Factor 3 ({factor_cols[2]})', fontsize=12)
axes[2].set_title('Factor 2 vs Factor 3', fontsize=14, fontweight='bold')
axes[2].grid(True, alpha=0.3)

# Add legend to the last subplot
axes[2].legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=10)

plt.suptitle(f'2D Projections of {entity.title()} Clusters', fontsize=16, fontweight='bold')
plt.tight_layout()

# Save the plot
output_file = os.path.join(output_dir, f"2d_projections_{entity}s.png")
plt.savefig(output_file, dpi=300, bbox_inches='tight', facecolor='white')
print(f"2D projections saved to: {output_file}")

plt.show()
#%% md
## Cluster Statistics
#%%
# Calculate cluster statistics
print("=" * 60)
print("CLUSTER ANALYSIS SUMMARY")
print("=" * 60)
print(f"Total {entity}s: {len(plot_df)}")
print(f"Number of clusters: {n_clusters}")
print(f"Latent factors used: {list(factor_cols)}")
print()

# Statistics for each cluster
for cluster in unique_clusters:
    cluster_data = plot_df[plot_df['Cluster'] == cluster]
    print(f"Cluster {cluster}:")
    print(f"  Size: {len(cluster_data)} {entity}s ({len(cluster_data)/len(plot_df)*100:.1f}%)")
    print(f"  Factor 1 - Mean: {cluster_data['Factor_1'].mean():.3f}, Std: {cluster_data['Factor_1'].std():.3f}")
    print(f"  Factor 2 - Mean: {cluster_data['Factor_2'].mean():.3f}, Std: {cluster_data['Factor_2'].std():.3f}")
    print(f"  Factor 3 - Mean: {cluster_data['Factor_3'].mean():.3f}, Std: {cluster_data['Factor_3'].std():.3f}")
    print()

# Overall statistics
print("OVERALL FACTOR STATISTICS:")
print(f"Factor 1 - Range: [{plot_df['Factor_1'].min():.3f}, {plot_df['Factor_1'].max():.3f}]")
print(f"Factor 2 - Range: [{plot_df['Factor_2'].min():.3f}, {plot_df['Factor_2'].max():.3f}]")
print(f"Factor 3 - Range: [{plot_df['Factor_3'].min():.3f}, {plot_df['Factor_3'].max():.3f}]")

print("\n" + "=" * 60)
print("VISUALIZATION FILES CREATED:")
print("=" * 60)
if PLOTLY_AVAILABLE:
    print(f"✅ Interactive 3D plot: 3d_cluster_visualization_{entity}s_interactive.html")
print(f"✅ Static 3D plot: 3d_cluster_visualization_{entity}s_static.png")
print(f"✅ 2D projections: 2d_projections_{entity}s.png")
print(f"📁 All files saved to: {output_dir}")
#%% md
## Summary

This robust notebook creates comprehensive 3D visualizations of your clusters:

### **Features:**
1. **Automatic Plotly Detection**: Works with or without plotly installed
2. **Interactive 3D Plot**: If plotly is available, creates interactive visualization
3. **Static 3D Plot**: Always creates high-quality matplotlib visualization
4. **2D Projections**: Shows cluster separation in different factor combinations
5. **Detailed Statistics**: Comprehensive cluster analysis

### **Output Files:**
- **Interactive HTML** (if plotly available): Rotatable, zoomable 3D plot
- **Static PNG**: High-resolution 3D visualization for presentations
- **2D Projections**: Multiple views of cluster separation

The notebook automatically adapts to your environment and will work regardless of whether plotly is installed!