#%% md
# 3D Cluster Visualization (Robust Version)
This notebook creates 3D plots of the clusters using the three latent factors. It works with or without plotly.
#%%
import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D

# Try to import plotly, fall back to matplotlib-only if not available
try:
    import plotly.express as px
    import plotly.graph_objects as go
    PLOTLY_AVAILABLE = True
    print("✅ Plotly is available - Interactive plots enabled")
except ImportError:
    PLOTLY_AVAILABLE = False
    print("⚠️  Plotly not available - Using matplotlib only")
    print("   To install plotly: pip install plotly")

# Set style
plt.style.use("ggplot")
plt.rcParams['figure.facecolor'] = 'white'
#%%
# Configuration
data_location = "/mnt/c/Users/<USER>/Downloads/segmentation_3010/30102025050705.xlsx"
user_col_name = "sys_Respondent"
entity = "user"  # or "item"
output_dir = "/mnt/c/Users/<USER>/Downloads/segmentation_3010/"
#%% md
## Load Data
#%%
# Determine sheet names based on entity type
if entity == "user":
    sheet_name = "Users"
    entity_info_cols = [user_col_name, "user_idx"]
    cluster_info_sheet_name = "Clusters (Users)"
    cluster_assignment_sheet_name = "Cluster Assignment (Users)"
    num_entities_col = "number_of_users"
else:
    sheet_name = "Items"
    entity_info_cols = ["item", "item_idx"]
    cluster_info_sheet_name = "Clusters (Items)"
    cluster_assignment_sheet_name = "Cluster Assignment (Items)"
    num_entities_col = "number_of_items"

# Load the data
print("Loading data...")
latent_factors_df = pd.read_excel(data_location, sheet_name=sheet_name)
clusters_df = pd.read_excel(data_location, sheet_name=cluster_info_sheet_name)
assignments_df = pd.read_excel(data_location, sheet_name=cluster_assignment_sheet_name)

print(f"Loaded {len(latent_factors_df)} {entity}s with {latent_factors_df.shape[1]-2} latent factors")
print(f"Found {len(clusters_df)} clusters")
print("Data loaded successfully!")
#%%
# Extract the three latent factors
latent_factors_only = latent_factors_df.drop(entity_info_cols, axis=1)
factor_cols = latent_factors_only.columns[:3]  # Take first 3 latent factors

print(f"Using latent factors: {list(factor_cols)}")
print(f"Latent factors shape: {latent_factors_only[factor_cols].shape}")

# Create the plotting dataframe
plot_df = pd.DataFrame({
    'Factor_1': latent_factors_only[factor_cols[0]],
    'Factor_2': latent_factors_only[factor_cols[1]], 
    'Factor_3': latent_factors_only[factor_cols[2]],
    'Cluster': assignments_df['cluster'],
    'Entity_ID': assignments_df[entity_info_cols[0]] if entity_info_cols[0] in assignments_df.columns else range(len(assignments_df))
})

print(f"Plot dataframe shape: {plot_df.shape}")
print(f"Clusters found: {sorted(plot_df['Cluster'].unique())}")
#%% md
## Create Color Palette
#%%
# Create a color map for clusters
unique_clusters = sorted(plot_df['Cluster'].unique())
n_clusters = len(unique_clusters)

# Use appropriate color palette based on available libraries
if PLOTLY_AVAILABLE:
    colors = px.colors.qualitative.Set3[:n_clusters] if n_clusters <= 12 else px.colors.qualitative.Light24[:n_clusters]
    color_map = {cluster: colors[i] for i, cluster in enumerate(unique_clusters)}
else:
    # Use matplotlib colors if plotly not available
    colors = plt.cm.tab10(np.linspace(0, 1, min(10, n_clusters)))
    if n_clusters > 10:
        colors = plt.cm.tab20(np.linspace(0, 1, n_clusters))
    color_map = {cluster: colors[i % len(colors)] for i, cluster in enumerate(unique_clusters)}

print(f"Created color map for {n_clusters} clusters")
for cluster in unique_clusters:
    cluster_size = len(plot_df[plot_df['Cluster'] == cluster])
    print(f"Cluster {cluster}: {cluster_size} {entity}s")
#%% md
## Interactive 3D Plot with Plotly (if available)
#%%
if PLOTLY_AVAILABLE:
    print("Creating interactive 3D plot with Plotly...")
    
    # Create interactive 3D scatter plot
    fig = go.Figure()
    
    for cluster in unique_clusters:
        cluster_data = plot_df[plot_df['Cluster'] == cluster]
        cluster_size = len(clusters_df[clusters_df['cluster'] == cluster][num_entities_col].values)
        if cluster_size > 0:
            cluster_size = clusters_df[clusters_df['cluster'] == cluster][num_entities_col].values[0]
        else:
            cluster_size = len(cluster_data)
        
        fig.add_trace(go.Scatter3d(
            x=cluster_data['Factor_1'],
            y=cluster_data['Factor_2'],
            z=cluster_data['Factor_3'],
            mode='markers',
            marker=dict(
                size=6,
                color=color_map[cluster],
                opacity=0.7,
                line=dict(width=0.5, color='DarkSlateGrey')
            ),
            name=f'Cluster {cluster} ({cluster_size} {entity}s)',
            text=cluster_data['Entity_ID'],
            hovertemplate=
                f'<b>Cluster {cluster}</b><br>' +
                'Factor 1: %{x:.3f}<br>' +
                'Factor 2: %{y:.3f}<br>' +
                'Factor 3: %{z:.3f}<br>' +
                f'{entity.title()} ID: %{{text}}<br>' +
                '<extra></extra>'
        ))
    
    # Update layout
    fig.update_layout(
        title={
            'text': f'Interactive 3D Cluster Visualization - {entity.title()}s in Latent Factor Space',
            'x': 0.5,
            'xanchor': 'center',
            'font': {'size': 20}
        },
        scene=dict(
            xaxis_title=f'Latent Factor 1 ({factor_cols[0]})',
            yaxis_title=f'Latent Factor 2 ({factor_cols[1]})',
            zaxis_title=f'Latent Factor 3 ({factor_cols[2]})',
            camera=dict(
                eye=dict(x=1.5, y=1.5, z=1.5)
            )
        ),
        width=1000,
        height=800,
        legend=dict(
            yanchor="top",
            y=0.99,
            xanchor="left",
            x=0.01
        )
    )
    
    # Show the plot
    fig.show()
    
    # Save as HTML
    output_file = os.path.join(output_dir, f"3d_cluster_visualization_{entity}s_interactive.html")
    fig.write_html(output_file)
    print(f"Interactive 3D plot saved to: {output_file}")
    
else:
    print("Plotly not available - skipping interactive plot")
    print("Install plotly with: pip install plotly")
#%% md
## Static 3D Plot with Matplotlib
#%%
print("Creating static 3D plot with Matplotlib...")

# Create static 3D plot with matplotlib
fig = plt.figure(figsize=(16, 12))
ax = fig.add_subplot(111, projection='3d')

# Plot each cluster
for cluster in unique_clusters:
    cluster_data = plot_df[plot_df['Cluster'] == cluster]
    try:
        cluster_size = clusters_df[clusters_df['cluster'] == cluster][num_entities_col].values[0]
    except:
        cluster_size = len(cluster_data)
    
    ax.scatter(
        cluster_data['Factor_1'],
        cluster_data['Factor_2'], 
        cluster_data['Factor_3'],
        c=[color_map[cluster]],
        label=f'Cluster {cluster} ({cluster_size} {entity}s)',
        alpha=0.7,
        s=60,
        edgecolors='black',
        linewidth=0.5
    )

# Customize the plot
ax.set_xlabel(f'Latent Factor 1\n({factor_cols[0]})', fontsize=12, labelpad=10)
ax.set_ylabel(f'Latent Factor 2\n({factor_cols[1]})', fontsize=12, labelpad=10)
ax.set_zlabel(f'Latent Factor 3\n({factor_cols[2]})', fontsize=12, labelpad=10)

ax.set_title(f'3D Cluster Visualization\n{entity.title()}s in Latent Factor Space', 
             fontsize=18, fontweight='bold', pad=30)

# Add legend
ax.legend(bbox_to_anchor=(1.15, 1), loc='upper left', fontsize=11)

# Set viewing angle for better perspective
ax.view_init(elev=20, azim=45)

# Add grid
ax.grid(True, alpha=0.3)

# Adjust layout
plt.tight_layout()

# Save the plot
output_file = os.path.join(output_dir, f"3d_cluster_visualization_{entity}s_static.png")
plt.savefig(output_file, dpi=300, bbox_inches='tight', facecolor='white')
print(f"Static 3D plot saved to: {output_file}")

plt.show()
#%% md
## 2D Projections
#%%
# Create 2D projections
fig, axes = plt.subplots(1, 3, figsize=(18, 6))

# Factor 1 vs Factor 2
for cluster in unique_clusters:
    cluster_data = plot_df[plot_df['Cluster'] == cluster]
    axes[0].scatter(cluster_data['Factor_1'], cluster_data['Factor_2'], 
                   c=[color_map[cluster]], label=f'Cluster {cluster}', alpha=0.7, s=50)
axes[0].set_xlabel(f'Latent Factor 1 ({factor_cols[0]})', fontsize=12)
axes[0].set_ylabel(f'Latent Factor 2 ({factor_cols[1]})', fontsize=12)
axes[0].set_title('Factor 1 vs Factor 2', fontsize=14, fontweight='bold')
axes[0].grid(True, alpha=0.3)

# Factor 1 vs Factor 3
for cluster in unique_clusters:
    cluster_data = plot_df[plot_df['Cluster'] == cluster]
    axes[1].scatter(cluster_data['Factor_1'], cluster_data['Factor_3'], 
                   c=[color_map[cluster]], label=f'Cluster {cluster}', alpha=0.7, s=50)
axes[1].set_xlabel(f'Latent Factor 1 ({factor_cols[0]})', fontsize=12)
axes[1].set_ylabel(f'Latent Factor 3 ({factor_cols[2]})', fontsize=12)
axes[1].set_title('Factor 1 vs Factor 3', fontsize=14, fontweight='bold')
axes[1].grid(True, alpha=0.3)

# Factor 2 vs Factor 3
for cluster in unique_clusters:
    cluster_data = plot_df[plot_df['Cluster'] == cluster]
    axes[2].scatter(cluster_data['Factor_2'], cluster_data['Factor_3'], 
                   c=[color_map[cluster]], label=f'Cluster {cluster}', alpha=0.7, s=50)
axes[2].set_xlabel(f'Latent Factor 2 ({factor_cols[1]})', fontsize=12)
axes[2].set_ylabel(f'Latent Factor 3 ({factor_cols[2]})', fontsize=12)
axes[2].set_title('Factor 2 vs Factor 3', fontsize=14, fontweight='bold')
axes[2].grid(True, alpha=0.3)

# Add legend to the last subplot
axes[2].legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=10)

plt.suptitle(f'2D Projections of {entity.title()} Clusters', fontsize=16, fontweight='bold')
plt.tight_layout()

# Save the plot
output_file = os.path.join(output_dir, f"2d_projections_{entity}s.png")
plt.savefig(output_file, dpi=300, bbox_inches='tight', facecolor='white')
print(f"2D projections saved to: {output_file}")

plt.show()
#%% md
## Cluster Statistics
#%%
# Calculate cluster statistics
print("=" * 60)
print("CLUSTER ANALYSIS SUMMARY")
print("=" * 60)
print(f"Total {entity}s: {len(plot_df)}")
print(f"Number of clusters: {n_clusters}")
print(f"Latent factors used: {list(factor_cols)}")
print()

# Statistics for each cluster
for cluster in unique_clusters:
    cluster_data = plot_df[plot_df['Cluster'] == cluster]
    print(f"Cluster {cluster}:")
    print(f"  Size: {len(cluster_data)} {entity}s ({len(cluster_data)/len(plot_df)*100:.1f}%)")
    print(f"  Factor 1 - Mean: {cluster_data['Factor_1'].mean():.3f}, Std: {cluster_data['Factor_1'].std():.3f}")
    print(f"  Factor 2 - Mean: {cluster_data['Factor_2'].mean():.3f}, Std: {cluster_data['Factor_2'].std():.3f}")
    print(f"  Factor 3 - Mean: {cluster_data['Factor_3'].mean():.3f}, Std: {cluster_data['Factor_3'].std():.3f}")
    print()

# Overall statistics
print("OVERALL FACTOR STATISTICS:")
print(f"Factor 1 - Range: [{plot_df['Factor_1'].min():.3f}, {plot_df['Factor_1'].max():.3f}]")
print(f"Factor 2 - Range: [{plot_df['Factor_2'].min():.3f}, {plot_df['Factor_2'].max():.3f}]")
print(f"Factor 3 - Range: [{plot_df['Factor_3'].min():.3f}, {plot_df['Factor_3'].max():.3f}]")

print("\n" + "=" * 60)
print("VISUALIZATION FILES CREATED:")
print("=" * 60)
if PLOTLY_AVAILABLE:
    print(f"✅ Interactive 3D plot: 3d_cluster_visualization_{entity}s_interactive.html")
print(f"✅ Static 3D plot: 3d_cluster_visualization_{entity}s_static.png")
print(f"✅ 2D projections: 2d_projections_{entity}s.png")
print(f"📁 All files saved to: {output_dir}")
#%% md
## Summary

This robust notebook creates comprehensive 3D visualizations of your clusters:

### **Features:**
1. **Automatic Plotly Detection**: Works with or without plotly installed
2. **Interactive 3D Plot**: If plotly is available, creates interactive visualization
3. **Static 3D Plot**: Always creates high-quality matplotlib visualization
4. **2D Projections**: Shows cluster separation in different factor combinations
5. **Detailed Statistics**: Comprehensive cluster analysis

### **Output Files:**
- **Interactive HTML** (if plotly available): Rotatable, zoomable 3D plot
- **Static PNG**: High-resolution 3D visualization for presentations
- **2D Projections**: Multiple views of cluster separation

The notebook automatically adapts to your environment and will work regardless of whether plotly is installed!